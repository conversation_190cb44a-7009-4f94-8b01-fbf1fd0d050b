<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>你喜欢我吗？💕</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            position: relative;
        }

        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            padding: 60px 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 90%;
            position: relative;
            z-index: 10;
        }

        .avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 0 auto 30px;
            border: 4px solid #fff;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .avatar:hover {
            transform: scale(1.05);
        }

        h1 {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 20px;
            font-weight: 700;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 40px;
            line-height: 1.6;
        }

        .buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-yes {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
            min-width: 120px;
        }

        .btn-yes:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(255, 107, 107, 0.4);
        }

        .btn-no {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
            color: white;
            box-shadow: 0 10px 30px rgba(116, 185, 255, 0.3);
            min-width: 120px;
            transition: all 0.3s ease;
        }

        .btn-no:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(116, 185, 255, 0.4);
        }

        .hearts {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .heart {
            position: absolute;
            color: #ff6b6b;
            font-size: 20px;
            animation: float 3s ease-in-out infinite;
            opacity: 0;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        .success-message {
            display: none;
            text-align: center;
            color: #333;
        }

        .success-message h2 {
            font-size: 2rem;
            color: #ff6b6b;
            margin-bottom: 20px;
        }

        .success-message p {
            font-size: 1.2rem;
            line-height: 1.6;
        }

        @media (max-width: 480px) {
            .container {
                padding: 40px 20px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .buttons {
                flex-direction: column;
                gap: 15px;
            }
            
            .btn {
                width: 100%;
                max-width: 200px;
            }
        }

        .floating-images {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .floating-img {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            opacity: 0.7;
            animation: floatImage 8s linear infinite;
        }

        @keyframes floatImage {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0.7;
            }
            50% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        /* 搞怪效果样式 */
        .shake {
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
            20%, 40%, 60%, 80% { transform: translateX(10px); }
        }

        .invert {
            filter: invert(1) hue-rotate(180deg);
            transition: filter 0.3s ease;
        }

        .glitch {
            animation: glitch 0.3s ease-in-out;
        }

        @keyframes glitch {
            0% { transform: translate(0); }
            20% { transform: translate(-2px, 2px); }
            40% { transform: translate(-2px, -2px); }
            60% { transform: translate(2px, 2px); }
            80% { transform: translate(2px, -2px); }
            100% { transform: translate(0); }
        }

        .spin-crazy {
            animation: spinCrazy 1s ease-in-out;
        }

        @keyframes spinCrazy {
            0% { transform: rotate(0deg) scale(1); }
            25% { transform: rotate(90deg) scale(1.2); }
            50% { transform: rotate(180deg) scale(0.8); }
            75% { transform: rotate(270deg) scale(1.1); }
            100% { transform: rotate(360deg) scale(1); }
        }

        /* 黑客矩阵效果 */
        .matrix-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #000;
            z-index: 1000;
            display: none;
        }

        .matrix-canvas {
            width: 100%;
            height: 100%;
        }

        .matrix-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 1.5rem;
            text-align: center;
            z-index: 1001;
            opacity: 0;
            animation: fadeInMatrix 2s ease-in-out 1s forwards;
        }

        @keyframes fadeInMatrix {
            0% { opacity: 0; transform: translate(-50%, -50%) scale(0.5); }
            100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        }

        /* 手机振动效果 */
        .vibrate {
            animation: vibrate 0.1s linear infinite;
        }

        @keyframes vibrate {
            0% { transform: translate(0); }
            25% { transform: translate(1px, 1px); }
            50% { transform: translate(-1px, -1px); }
            75% { transform: translate(1px, -1px); }
            100% { transform: translate(-1px, 1px); }
        }
    </style>
</head>
<body>
    <div class="hearts" id="hearts"></div>
    <div class="floating-images" id="floatingImages"></div>
    
    <div class="container" id="mainContainer">
        <img src="008xLqLsly1hxc7p0ucumj30xc0xcn1k.jpg" alt="可爱头像" class="avatar" id="avatar">
        <h1>你喜欢我吗？</h1>
        <p class="subtitle">请诚实回答哦~ 💕</p>
        
        <div class="buttons">
            <button class="btn btn-yes" id="yesBtn" onclick="handleYes()">
                喜欢 💖
            </button>
            <button class="btn btn-no" id="noBtn" onclick="handleNo()">
                不喜欢
            </button>
        </div>
    </div>

    <div class="success-message" id="successMessage">
        <h2>太好了！💕</h2>
        <p>我就知道你会喜欢我的~<br>我们永远在一起吧！</p>
        <img src="008xLqLsly1hyquqry02qj30xc0xctch.jpg" alt="开心" style="width: 200px; height: 200px; object-fit: cover; border-radius: 20px; margin-top: 20px;">
    </div>

    <!-- 黑客矩阵效果 -->
    <div class="matrix-container" id="matrixContainer">
        <canvas class="matrix-canvas" id="matrixCanvas"></canvas>
        <div class="matrix-message" id="matrixMessage">
            <h2>💖 LOVE CONFIRMED 💖</h2>
            <p>系统检测到：真爱已连接</p>
            <p>正在建立永久连接...</p>
            <p>连接成功！💕</p>
        </div>
    </div>

    <script>
        let noClickCount = 0;
        const images = [
            '008xLqLsly1hwj9cte0rrg306o06oq3d.jfif',
            '008xLqLsly1hwj9ctesm9g306o06oaap.jfif',
            '008xLqLsly1hwj9cthyw1g306o06on2d.jfif',
            '008xLqLsly1hxfq5ccsxbg306o06otdw.jfif',
            '008xLqLsly1i02bncaq4cg306o06ojsz.jfif',
            'starteyes.jfif'
        ];

        function createHeart() {
            const heart = document.createElement('div');
            heart.className = 'heart';
            heart.innerHTML = '💖';
            heart.style.left = Math.random() * 100 + '%';
            heart.style.animationDelay = Math.random() * 2 + 's';
            heart.style.animationDuration = (Math.random() * 2 + 3) + 's';
            document.getElementById('hearts').appendChild(heart);
            
            setTimeout(() => {
                heart.remove();
            }, 5000);
        }

        function createFloatingImage() {
            const img = document.createElement('img');
            img.className = 'floating-img';
            img.src = images[Math.floor(Math.random() * images.length)];
            img.style.left = Math.random() * 100 + '%';
            img.style.animationDelay = Math.random() * 2 + 's';
            img.style.animationDuration = (Math.random() * 3 + 6) + 's';
            document.getElementById('floatingImages').appendChild(img);
            
            setTimeout(() => {
                img.remove();
            }, 10000);
        }

        function handleYes() {
            // 触发手机振动
            if (navigator.vibrate) {
                navigator.vibrate([200, 100, 200, 100, 200]);
            }

            // 显示黑客矩阵效果
            showMatrixEffect();

            // 创建大量爱心和图片
            for (let i = 0; i < 30; i++) {
                setTimeout(() => createHeart(), i * 50);
                setTimeout(() => createFloatingImage(), i * 80);
            }
        }

        function showMatrixEffect() {
            const matrixContainer = document.getElementById('matrixContainer');
            const canvas = document.getElementById('matrixCanvas');
            const ctx = canvas.getContext('2d');

            matrixContainer.style.display = 'block';

            // 设置canvas尺寸
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;

            // 矩阵字符
            const chars = '01💖💕❤️😘🥰😍💋👫💑🌹✨💫⭐️🎉🎊';
            const charArray = chars.split('');

            const fontSize = 14;
            const columns = canvas.width / fontSize;
            const drops = [];

            // 初始化雨滴
            for (let x = 0; x < columns; x++) {
                drops[x] = 1;
            }

            function drawMatrix() {
                ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                ctx.fillStyle = '#00ff00';
                ctx.font = fontSize + 'px monospace';

                for (let i = 0; i < drops.length; i++) {
                    const text = charArray[Math.floor(Math.random() * charArray.length)];
                    ctx.fillText(text, i * fontSize, drops[i] * fontSize);

                    if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
                        drops[i] = 0;
                    }
                    drops[i]++;
                }
            }

            const matrixInterval = setInterval(drawMatrix, 50);

            // 5秒后显示最终成功页面
            setTimeout(() => {
                clearInterval(matrixInterval);
                matrixContainer.style.display = 'none';
                document.getElementById('mainContainer').style.display = 'none';
                document.getElementById('successMessage').style.display = 'block';
            }, 5000);
        }

        function handleNo() {
            noClickCount++;
            const yesBtn = document.getElementById('yesBtn');
            const noBtn = document.getElementById('noBtn');
            const avatar = document.getElementById('avatar');
            const container = document.getElementById('mainContainer');

            // 手机振动
            if (navigator.vibrate) {
                navigator.vibrate([100, 50, 100]);
            }

            // 各种搞怪效果
            switch(noClickCount) {
                case 1:
                    // 屏幕震动 + 放大yes按钮
                    document.body.classList.add('shake');
                    setTimeout(() => document.body.classList.remove('shake'), 500);

                    yesBtn.style.transform = 'scale(1.2)';
                    noBtn.style.transform = 'scale(0.9)';
                    noBtn.innerHTML = '真的不喜欢？';
                    avatar.src = '008xLqLsly1i0pcras7udj30go0go0u4.jpg';
                    break;

                case 2:
                    // 颜色反转效果
                    document.body.classList.add('invert');
                    setTimeout(() => document.body.classList.remove('invert'), 1000);

                    yesBtn.style.transform = 'scale(1.4)';
                    noBtn.style.transform = 'scale(0.7)';
                    noBtn.innerHTML = '再想想？';
                    avatar.src = '008xLqLsly1i0pcratvk3j30go0go3zz.jpg';
                    break;

                case 3:
                    // 故障效果 + no按钮开始移动
                    container.classList.add('glitch');
                    setTimeout(() => container.classList.remove('glitch'), 300);

                    yesBtn.style.transform = 'scale(1.6)';
                    noBtn.style.transform = 'scale(0.5)';
                    noBtn.style.position = 'absolute';

                    // 移动端适配的随机位置
                    const maxX = window.innerWidth - 120;
                    const maxY = window.innerHeight - 60;
                    noBtn.style.left = Math.random() * maxX + 'px';
                    noBtn.style.top = Math.random() * maxY + 'px';
                    noBtn.innerHTML = '别点我！';
                    avatar.src = '008xLqLsly1hz72ypbdjbj30zu0zhtgh.jpg';
                    break;

                case 4:
                    // 疯狂旋转 + 更小的no按钮
                    avatar.classList.add('spin-crazy');
                    setTimeout(() => avatar.classList.remove('spin-crazy'), 1000);

                    yesBtn.style.transform = 'scale(1.8)';
                    noBtn.style.transform = 'scale(0.3)';

                    const maxX2 = window.innerWidth - 80;
                    const maxY2 = window.innerHeight - 40;
                    noBtn.style.left = Math.random() * maxX2 + 'px';
                    noBtn.style.top = Math.random() * maxY2 + 'px';
                    noBtn.innerHTML = '😭';
                    break;

                case 5:
                    // 整个页面振动
                    document.body.classList.add('vibrate');
                    setTimeout(() => document.body.classList.remove('vibrate'), 2000);

                    yesBtn.style.transform = 'scale(2)';
                    noBtn.style.transform = 'scale(0.2)';
                    noBtn.innerHTML = '💔';
                    break;

                default:
                    // 最终no按钮消失，只能选yes
                    if (noClickCount >= 6) {
                        noBtn.style.display = 'none';
                        yesBtn.style.transform = 'scale(2.2)';
                        yesBtn.innerHTML = '只能选我了~ 💖';
                        yesBtn.style.animation = 'pulse 1s infinite';

                        // 最后的庆祝效果
                        for (let i = 0; i < 10; i++) {
                            setTimeout(() => createHeart(), i * 100);
                        }
                    }
                    break;
            }

            // 添加一些特效
            createHeart();
            createFloatingImage();
        }

        // 定期创建背景特效
        setInterval(createHeart, 2000);
        setInterval(createFloatingImage, 3000);

        // 添加脉冲动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% { transform: scale(2); }
                50% { transform: scale(2.1); }
                100% { transform: scale(2); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
