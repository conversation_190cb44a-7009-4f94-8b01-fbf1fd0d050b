<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>你喜欢我吗？💕</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            position: relative;
        }

        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            padding: 60px 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 90%;
            position: relative;
            z-index: 10;
        }

        .avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 0 auto 30px;
            border: 4px solid #fff;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .avatar:hover {
            transform: scale(1.05);
        }

        h1 {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 20px;
            font-weight: 700;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 40px;
            line-height: 1.6;
        }

        .buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-yes {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
            min-width: 120px;
        }

        .btn-yes:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(255, 107, 107, 0.4);
        }

        .btn-no {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
            color: white;
            box-shadow: 0 10px 30px rgba(116, 185, 255, 0.3);
            min-width: 120px;
            transition: all 0.3s ease;
        }

        .btn-no:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(116, 185, 255, 0.4);
        }

        .hearts {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .heart {
            position: absolute;
            color: #ff6b6b;
            font-size: 20px;
            animation: float 3s ease-in-out infinite;
            opacity: 0;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        .success-message {
            display: none;
            text-align: center;
            color: #333;
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s ease-out;
            position: relative;
            z-index: 60;
        }

        .success-message.show {
            opacity: 1;
            transform: translateY(0);
        }

        .success-message h2 {
            font-size: 2rem;
            color: #ff6b6b;
            margin-bottom: 20px;
            animation: pulse 2s ease-in-out infinite;
        }

        .success-message p {
            font-size: 1.2rem;
            line-height: 1.6;
        }

        .success-image {
            width: 200px;
            height: 200px;
            object-fit: cover;
            border-radius: 20px;
            margin-top: 20px;
            box-shadow: 0 15px 40px rgba(255, 107, 107, 0.3);
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        @media (max-width: 480px) {
            .container {
                padding: 40px 20px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .buttons {
                flex-direction: column;
                gap: 15px;
            }
            
            .btn {
                width: 100%;
                max-width: 200px;
            }
        }

        .floating-images {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .floating-img {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            opacity: 0.7;
            animation: floatImage 8s linear infinite;
        }

        @keyframes floatImage {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0.7;
            }
            50% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        /* 屏幕反转效果 */
        .inverted {
            filter: invert(1) hue-rotate(180deg);
            transition: filter 0.5s ease;
        }

        /* 震动效果 */
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
            20%, 40%, 60%, 80% { transform: translateX(10px); }
        }

        .shake {
            animation: shake 0.5s ease-in-out;
        }

        /* 旋转效果 */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .spin {
            animation: spin 1s ease-in-out;
        }

        /* 闪烁效果 */
        @keyframes blink {
            0%, 50% { opacity: 1; }
            25%, 75% { opacity: 0.3; }
        }

        .blink {
            animation: blink 0.5s ease-in-out 3;
        }

        /* 图片矩阵雨效果 */
        .matrix-rain {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 50;
            background: #000;
            display: none;
            opacity: 0;
            transition: opacity 0.8s ease-in-out;
            overflow: hidden;
        }

        .matrix-rain.show {
            opacity: 1;
            animation: screenBrighten 1.5s ease-out forwards;
        }

        /* 矩阵雨密度控制 */
        .matrix-rain.dense {
            --column-count: 1;
        }

        .matrix-rain.medium {
            --column-count: 0.6;
        }

        .matrix-rain.sparse {
            --column-count: 0.3;
        }

        .matrix-rain::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 30%, rgba(255, 107, 107, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(116, 185, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(255, 20, 147, 0.05) 0%, transparent 70%);
            animation: backgroundPulse 4s ease-in-out infinite;
        }

        @keyframes backgroundPulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.7; }
        }

        .matrix-column {
            position: absolute;
            top: -300px;
            width: 80px;
            animation: matrixFall linear infinite;
            filter: drop-shadow(0 0 10px rgba(255, 107, 107, 0.3));
        }

        .matrix-image {
            width: 60px;
            height: 60px;
            margin: 8px auto;
            border-radius: 12px;
            object-fit: cover;
            opacity: 0;
            border: 2px solid rgba(255, 255, 255, 0.3);
            transition: all 0.4s ease;
            position: relative;
            animation: imageGlow 2s ease-in-out infinite alternate;
        }

        .matrix-image::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #ff6b6b, #74b9ff, #fd79a8, #fdcb6e);
            border-radius: 14px;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .matrix-image:hover::before {
            opacity: 0.7;
        }

        @keyframes imageGlow {
            0% {
                box-shadow:
                    0 0 20px rgba(255, 107, 107, 0.4),
                    0 0 40px rgba(255, 107, 107, 0.2),
                    inset 0 0 20px rgba(255, 255, 255, 0.1);
            }
            100% {
                box-shadow:
                    0 0 30px rgba(116, 185, 255, 0.4),
                    0 0 60px rgba(116, 185, 255, 0.2),
                    inset 0 0 20px rgba(255, 255, 255, 0.2);
            }
        }

        @keyframes matrixFall {
            0% {
                transform: translateY(-300px) rotateY(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(calc(100vh + 300px)) rotateY(360deg);
                opacity: 0;
            }
        }

        .matrix-image:nth-child(odd) {
            animation-delay: 0.2s;
        }

        .matrix-image:nth-child(even) {
            animation-delay: 0.4s;
        }

        /* 添加粒子效果 */
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: radial-gradient(circle, #ff6b6b 0%, transparent 70%);
            border-radius: 50%;
            animation: particleFloat 3s linear infinite;
            pointer-events: none;
        }

        @keyframes particleFloat {
            0% {
                transform: translateY(100vh) translateX(0px);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) translateX(50px);
                opacity: 0;
            }
        }

        /* 彩虹边框效果 */
        @keyframes rainbow {
            0% { border-color: #ff0000; }
            16% { border-color: #ff8000; }
            33% { border-color: #ffff00; }
            50% { border-color: #00ff00; }
            66% { border-color: #0080ff; }
            83% { border-color: #8000ff; }
            100% { border-color: #ff0000; }
        }

        .rainbow-border {
            border: 3px solid;
            animation: rainbow 2s linear infinite;
        }

        /* 弹跳效果 */
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-30px); }
            60% { transform: translateY(-15px); }
        }

        .bounce {
            animation: bounce 1s ease-in-out;
        }

        /* 断电闪电效果 */
        .blackout-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #000;
            z-index: 200;
            display: none;
            opacity: 0;
        }

        .lightning-flash {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(ellipse at 30% 20%, #ffffff 0%, transparent 50%),
                radial-gradient(ellipse at 70% 80%, #e1f5fe 0%, transparent 60%),
                linear-gradient(45deg, #ffffff 0%, #bbdefb 30%, #ffffff 60%, #e3f2fd 100%);
            z-index: 150;
            display: none;
            opacity: 0;
            box-shadow: inset 0 0 100px rgba(255, 255, 255, 0.8);
        }

        /* 闪电动画 - 更强烈更快速 */
        @keyframes lightning {
            0% {
                opacity: 0;
                transform: scale(1);
                filter: brightness(1);
            }
            3% {
                opacity: 1;
                transform: scale(1.02);
                filter: brightness(2);
            }
            6% {
                opacity: 0;
                transform: scale(1);
                filter: brightness(1);
            }
            10% {
                opacity: 1;
                transform: scale(1.01);
                filter: brightness(1.8);
            }
            14% {
                opacity: 0;
                transform: scale(1);
                filter: brightness(1);
            }
            18% {
                opacity: 1;
                transform: scale(1.03);
                filter: brightness(2.2);
            }
            25% {
                opacity: 0;
                transform: scale(1);
                filter: brightness(1);
            }
            100% {
                opacity: 0;
                transform: scale(1);
                filter: brightness(1);
            }
        }

        /* 断电动画 */
        @keyframes powerOut {
            0% { opacity: 1; }
            50% { opacity: 0.3; }
            100% { opacity: 0; }
        }

        /* 屏幕渐亮效果 - 更快更明显 */
        @keyframes screenBrighten {
            0% {
                background: #000;
                opacity: 1;
            }
            30% {
                background: linear-gradient(135deg, rgba(30, 30, 60, 0.9), rgba(60, 60, 100, 0.8));
                opacity: 0.9;
            }
            60% {
                background: linear-gradient(135deg, rgba(60, 60, 120, 0.7), rgba(100, 100, 160, 0.6));
                opacity: 0.8;
            }
            100% {
                background: radial-gradient(ellipse at center, rgba(255, 20, 147, 0.1) 0%, rgba(0, 0, 0, 0.95) 70%);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <div class="hearts" id="hearts"></div>
    <div class="floating-images" id="floatingImages"></div>
    <div class="lightning-flash" id="lightningFlash"></div>
    <div class="blackout-overlay" id="blackoutOverlay"></div>
    <div class="matrix-rain" id="matrixRain"></div>

    <div class="container" id="mainContainer">
        <img src="008xLqLsly1hxc7p0ucumj30xc0xcn1k.jpg" alt="可爱头像" class="avatar" id="avatar">
        <h1>你喜欢我吗？</h1>
        <p class="subtitle">请诚实回答哦~ 💕</p>
        
        <div class="buttons">
            <button class="btn btn-yes" id="yesBtn" onclick="handleYes()">
                喜欢 💖
            </button>
            <button class="btn btn-no" id="noBtn" onclick="handleNo()">
                不喜欢
            </button>
        </div>
    </div>

    <div class="success-message" id="successMessage">
        <h2>太好了！💕</h2>
        <p>我就知道你会喜欢我的~<br>我们永远在一起吧！</p>
        <img src="008xLqLsly1hyquqry02qj30xc0xctch.jpg" alt="开心" class="success-image">
    </div>

    <script>
        let noClickCount = 0;
        let isInverted = false;
        const images = [
            '008xLqLsly1hwj9cte0rrg306o06oq3d.jfif',
            '008xLqLsly1hwj9ctesm9g306o06oaap.jfif',
            '008xLqLsly1hwj9cthyw1g306o06on2d.jfif',
            '008xLqLsly1hxfq5ccsxbg306o06otdw.jfif',
            '008xLqLsly1i02bncaq4cg306o06ojsz.jfif',
            '008xLqLsly1i1u126597og306o06o0w4.jfif',
            '008xLqLsly1i1u1265dnvg306o06o0y5.jfif',
            '008xLqLsly1i320lfg9ngj30go0goq58.jpg',
            'starteyes.jfif'
        ];

        function createHeart() {
            const heart = document.createElement('div');
            heart.className = 'heart';
            heart.innerHTML = '💖';
            heart.style.left = Math.random() * 100 + '%';
            heart.style.animationDelay = Math.random() * 2 + 's';
            heart.style.animationDuration = (Math.random() * 2 + 3) + 's';
            document.getElementById('hearts').appendChild(heart);
            
            setTimeout(() => {
                heart.remove();
            }, 5000);
        }

        function createFloatingImage() {
            const img = document.createElement('img');
            img.className = 'floating-img';
            img.src = images[Math.floor(Math.random() * images.length)];
            img.style.left = Math.random() * 100 + '%';
            img.style.animationDelay = Math.random() * 2 + 's';
            img.style.animationDuration = (Math.random() * 3 + 6) + 's';
            document.getElementById('floatingImages').appendChild(img);
            
            setTimeout(() => {
                img.remove();
            }, 10000);
        }

        function createParticles() {
            const matrixContainer = document.getElementById('matrixRain');

            // 创建漂浮粒子
            for (let i = 0; i < 20; i++) {
                setTimeout(() => {
                    const particle = document.createElement('div');
                    particle.className = 'particle';
                    particle.style.left = Math.random() * window.innerWidth + 'px';
                    particle.style.animationDuration = (Math.random() * 2 + 2) + 's';
                    particle.style.animationDelay = Math.random() * 1 + 's';

                    matrixContainer.appendChild(particle);

                    setTimeout(() => {
                        if (particle.parentNode) {
                            particle.remove();
                        }
                    }, 4000);
                }, i * 100);
            }
        }

        function createMatrixRainPhase(density, delay = 0) {
            const matrixContainer = document.getElementById('matrixRain');

            setTimeout(() => {
                // 根据密度调整列数
                let columnSpacing, columnCount, imageCount;

                switch(density) {
                    case 'dense':
                        columnSpacing = 50;
                        columnCount = Math.floor(window.innerWidth / columnSpacing);
                        imageCount = [8, 12]; // 8-12张图片
                        break;
                    case 'medium':
                        columnSpacing = 80;
                        columnCount = Math.floor(window.innerWidth / columnSpacing);
                        imageCount = [5, 8]; // 5-8张图片
                        break;
                    case 'sparse':
                        columnSpacing = 120;
                        columnCount = Math.floor(window.innerWidth / columnSpacing);
                        imageCount = [3, 5]; // 3-5张图片
                        break;
                }

                for (let i = 0; i < columnCount; i++) {
                    setTimeout(() => {
                        const column = document.createElement('div');
                        column.className = 'matrix-column';
                        column.style.left = (i * columnSpacing + 15) + 'px';
                        column.style.animationDuration = (Math.random() * 1.5 + 3) + 's';
                        column.style.animationDelay = Math.random() * 0.5 + 's';

                        const imgCount = Math.floor(Math.random() * (imageCount[1] - imageCount[0] + 1)) + imageCount[0];
                        for (let j = 0; j < imgCount; j++) {
                            const img = document.createElement('img');
                            img.className = 'matrix-image';
                            img.src = images[Math.floor(Math.random() * images.length)];

                            setTimeout(() => {
                                img.style.opacity = '0.9';
                            }, j * 150);

                            column.appendChild(img);
                        }

                        matrixContainer.appendChild(column);

                        setTimeout(() => {
                            if (column.parentNode) {
                                column.remove();
                            }
                        }, 8000);
                    }, i * 80);
                }
            }, delay);
        }

        function createMatrixRain() {
            const matrixContainer = document.getElementById('matrixRain');
            matrixContainer.style.display = 'block';

            // 平滑显示矩阵雨
            setTimeout(() => {
                matrixContainer.classList.add('show');
            }, 100);

            // 创建粒子效果
            createParticles();

            // 阶段1: 密集矩阵雨 (0-2秒)
            createMatrixRainPhase('dense', 0);

            // 阶段2: 中等密度 (2-4秒)
            createMatrixRainPhase('medium', 2000);

            // 阶段3: 稀疏矩阵雨 (4-6秒)
            createMatrixRainPhase('sparse', 4000);

            // 持续创建粒子效果 - 增加粒子生成频率
            const particleInterval = setInterval(() => {
                if (matrixContainer.style.display !== 'none') {
                    createParticles();
                } else {
                    clearInterval(particleInterval);
                }
            }, 1500); // 从2000ms减少到1500ms，更频繁的粒子效果

            // 在3秒后再创建一轮新的图片列，让效果更丰富
            setTimeout(() => {
                if (matrixContainer.style.display !== 'none') {
                    // 重新计算列数
                    const baseColumns = Math.floor(window.innerWidth / 90);
                    const extraColumns = Math.floor(baseColumns / 2);
                    for (let i = 0; i < extraColumns; i++) {
                        setTimeout(() => {
                            const column = document.createElement('div');
                            column.className = 'matrix-column';
                            column.style.left = (i * 180 + 50) + 'px'; // 错开位置
                            column.style.animationDuration = (Math.random() * 1.5 + 3) + 's';
                            column.style.animationDelay = Math.random() * 0.5 + 's';

                            const imageCount = Math.floor(Math.random() * 2 + 5);
                            for (let j = 0; j < imageCount; j++) {
                                const img = document.createElement('img');
                                img.className = 'matrix-image';
                                img.src = images[Math.floor(Math.random() * images.length)];

                                setTimeout(() => {
                                    img.style.opacity = '0.9';
                                }, j * 150);

                                column.appendChild(img);
                            }

                            matrixContainer.appendChild(column);

                            setTimeout(() => {
                                if (column.parentNode) {
                                    column.remove();
                                }
                            }, 7000);
                        }, i * 100);
                    }
                }
            }, 3000);
        }

        function createLightningEffect() {
            const lightning = document.getElementById('lightningFlash');
            lightning.style.display = 'block';
            lightning.style.animation = 'lightning 0.6s ease-out';

            // 添加音效模拟（震动）
            if (navigator.vibrate) {
                navigator.vibrate([50, 30, 80, 20, 100]);
            }

            setTimeout(() => {
                lightning.style.display = 'none';
                lightning.style.animation = '';
            }, 600);
        }

        function createBlackoutEffect() {
            const blackout = document.getElementById('blackoutOverlay');
            const container = document.getElementById('mainContainer');

            // 强烈闪电效果
            createLightningEffect();

            // 容器快速断电效果
            setTimeout(() => {
                container.style.animation = 'powerOut 0.3s ease-out forwards';
            }, 400);

            // 快速黑屏
            setTimeout(() => {
                blackout.style.display = 'block';
                blackout.style.opacity = '1';
                container.style.display = 'none';
            }, 700);
        }

        function handleYes() {
            // 移除任何反转效果和特殊样式
            document.body.classList.remove('inverted');
            const container = document.getElementById('mainContainer');
            container.classList.remove('rainbow-border', 'shake', 'spin', 'bounce', 'blink');

            // 1. 断电闪电效果 (更快)
            createBlackoutEffect();

            // 2. 在黑屏后立即开始矩阵雨 (0.8秒后)
            setTimeout(() => {
                createMatrixRain();

                // 创建大量爱心
                for (let i = 0; i < 30; i++) {
                    setTimeout(() => createHeart(), i * 80);
                }
            }, 800);

            // 3. 矩阵雨结束后显示成功页面 (0.8 + 5秒后)
            setTimeout(() => {
                // 淡出矩阵雨和黑屏
                const matrixRain = document.getElementById('matrixRain');
                const blackout = document.getElementById('blackoutOverlay');

                matrixRain.classList.remove('show');
                blackout.style.opacity = '0';

                setTimeout(() => {
                    matrixRain.style.display = 'none';
                    blackout.style.display = 'none';

                    // 显示成功消息
                    const successMessage = document.getElementById('successMessage');
                    successMessage.style.display = 'block';

                    setTimeout(() => {
                        successMessage.classList.add('show');
                    }, 100);
                }, 600);
            }, 5800); // 0.8秒断电 + 5秒矩阵雨
        }

        function handleNo() {
            noClickCount++;
            const yesBtn = document.getElementById('yesBtn');
            const noBtn = document.getElementById('noBtn');
            const avatar = document.getElementById('avatar');
            const container = document.getElementById('mainContainer');

            // 手机震动
            if (navigator.vibrate) {
                navigator.vibrate(200);
            }

            // 各种搞怪效果
            switch(noClickCount) {
                case 1:
                    // 放大yes按钮，缩小no按钮，添加震动效果
                    yesBtn.style.transform = 'scale(1.2)';
                    noBtn.style.transform = 'scale(0.9)';
                    noBtn.innerHTML = '真的不喜欢？';
                    avatar.src = '008xLqLsly1i0pcras7udj30go0go0u4.jpg';
                    container.classList.add('shake');
                    setTimeout(() => container.classList.remove('shake'), 500);
                    break;

                case 2:
                    // 继续放大yes，继续缩小no，屏幕闪烁
                    yesBtn.style.transform = 'scale(1.4)';
                    noBtn.style.transform = 'scale(0.7)';
                    noBtn.innerHTML = '再想想？';
                    avatar.src = '008xLqLsly1i0pcratvk3j30go0go3zz.jpg';
                    document.body.classList.add('blink');
                    setTimeout(() => document.body.classList.remove('blink'), 1500);
                    break;

                case 3:
                    // no按钮开始移动，屏幕反转颜色
                    yesBtn.style.transform = 'scale(1.6)';
                    noBtn.style.transform = 'scale(0.5)';
                    noBtn.style.position = 'absolute';
                    noBtn.style.left = Math.random() * (window.innerWidth - 100) + 'px';
                    noBtn.style.top = Math.random() * (window.innerHeight - 100) + 'px';
                    noBtn.innerHTML = '别点我！';
                    avatar.src = '008xLqLsly1hz72ypbdjbj30zu0zhtgh.jpg';

                    // 屏幕反转效果
                    document.body.classList.toggle('inverted');
                    isInverted = !isInverted;
                    break;

                case 4:
                    // no按钮变得更小更难点，容器旋转
                    yesBtn.style.transform = 'scale(1.8)';
                    noBtn.style.transform = 'scale(0.3)';
                    noBtn.style.left = Math.random() * (window.innerWidth - 50) + 'px';
                    noBtn.style.top = Math.random() * (window.innerHeight - 50) + 'px';
                    noBtn.innerHTML = '😭';
                    container.classList.add('spin');
                    setTimeout(() => container.classList.remove('spin'), 1000);
                    break;

                case 5:
                    // 彩虹边框效果
                    yesBtn.style.transform = 'scale(2)';
                    noBtn.style.transform = 'scale(0.2)';
                    noBtn.style.left = Math.random() * (window.innerWidth - 30) + 'px';
                    noBtn.style.top = Math.random() * (window.innerHeight - 30) + 'px';
                    noBtn.innerHTML = '🥺';
                    container.classList.add('rainbow-border');
                    break;

                case 6:
                    // 弹跳效果
                    yesBtn.style.transform = 'scale(2.2)';
                    noBtn.style.transform = 'scale(0.1)';
                    noBtn.style.left = Math.random() * (window.innerWidth - 20) + 'px';
                    noBtn.style.top = Math.random() * (window.innerHeight - 20) + 'px';
                    container.classList.add('bounce');
                    setTimeout(() => container.classList.remove('bounce'), 1000);
                    break;

                default:
                    // 最终no按钮消失，只能选yes
                    if (noClickCount >= 7) {
                        noBtn.style.display = 'none';
                        yesBtn.style.transform = 'scale(2.5)';
                        yesBtn.innerHTML = '只能选我了~ 💖';
                        yesBtn.style.animation = 'pulse 1s infinite';

                        // 移除所有效果
                        document.body.classList.remove('inverted');
                        container.classList.remove('rainbow-border');
                    }
                    break;
            }

            // 添加一些特效
            createHeart();
            createFloatingImage();
        }

        // 定期创建背景特效
        setInterval(createHeart, 2000);
        setInterval(createFloatingImage, 3000);

        // 添加脉冲动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% { transform: scale(2); }
                50% { transform: scale(2.1); }
                100% { transform: scale(2); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
