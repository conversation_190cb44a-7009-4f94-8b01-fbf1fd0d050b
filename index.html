<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>你喜欢我吗？💕</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            position: relative;
        }

        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            padding: 60px 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 90%;
            position: relative;
            z-index: 10;
        }

        .avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 0 auto 30px;
            border: 4px solid #fff;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .avatar:hover {
            transform: scale(1.05);
        }

        h1 {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 20px;
            font-weight: 700;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 40px;
            line-height: 1.6;
        }

        .buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-yes {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
            min-width: 120px;
        }

        .btn-yes:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(255, 107, 107, 0.4);
        }

        .btn-no {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
            color: white;
            box-shadow: 0 10px 30px rgba(116, 185, 255, 0.3);
            min-width: 120px;
            transition: all 0.3s ease;
        }

        .btn-no:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(116, 185, 255, 0.4);
        }

        .hearts {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .heart {
            position: absolute;
            color: #ff6b6b;
            font-size: 20px;
            animation: float 3s ease-in-out infinite;
            opacity: 0;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        .success-message {
            display: none;
            text-align: center;
            color: #333;
        }

        .success-message h2 {
            font-size: 2rem;
            color: #ff6b6b;
            margin-bottom: 20px;
        }

        .success-message p {
            font-size: 1.2rem;
            line-height: 1.6;
        }

        @media (max-width: 480px) {
            .container {
                padding: 40px 20px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .buttons {
                flex-direction: column;
                gap: 15px;
            }
            
            .btn {
                width: 100%;
                max-width: 200px;
            }
        }

        .floating-images {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .floating-img {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            opacity: 0.7;
            animation: floatImage 8s linear infinite;
        }

        @keyframes floatImage {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0.7;
            }
            50% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <div class="hearts" id="hearts"></div>
    <div class="floating-images" id="floatingImages"></div>
    
    <div class="container" id="mainContainer">
        <img src="008xLqLsly1hxc7p0ucumj30xc0xcn1k.jpg" alt="可爱头像" class="avatar" id="avatar">
        <h1>你喜欢我吗？</h1>
        <p class="subtitle">请诚实回答哦~ 💕</p>
        
        <div class="buttons">
            <button class="btn btn-yes" id="yesBtn" onclick="handleYes()">
                喜欢 💖
            </button>
            <button class="btn btn-no" id="noBtn" onclick="handleNo()">
                不喜欢
            </button>
        </div>
    </div>

    <div class="success-message" id="successMessage">
        <h2>太好了！💕</h2>
        <p>我就知道你会喜欢我的~<br>我们永远在一起吧！</p>
        <img src="008xLqLsly1hyquqry02qj30xc0xctch.jpg" alt="开心" style="width: 200px; height: 200px; object-fit: cover; border-radius: 20px; margin-top: 20px;">
    </div>

    <script>
        let noClickCount = 0;
        const images = [
            '008xLqLsly1hwj9cte0rrg306o06oq3d.jfif',
            '008xLqLsly1hwj9ctesm9g306o06oaap.jfif',
            '008xLqLsly1hwj9cthyw1g306o06on2d.jfif',
            '008xLqLsly1hxfq5ccsxbg306o06otdw.jfif',
            '008xLqLsly1i02bncaq4cg306o06ojsz.jfif',
            'starteyes.jfif'
        ];

        function createHeart() {
            const heart = document.createElement('div');
            heart.className = 'heart';
            heart.innerHTML = '💖';
            heart.style.left = Math.random() * 100 + '%';
            heart.style.animationDelay = Math.random() * 2 + 's';
            heart.style.animationDuration = (Math.random() * 2 + 3) + 's';
            document.getElementById('hearts').appendChild(heart);
            
            setTimeout(() => {
                heart.remove();
            }, 5000);
        }

        function createFloatingImage() {
            const img = document.createElement('img');
            img.className = 'floating-img';
            img.src = images[Math.floor(Math.random() * images.length)];
            img.style.left = Math.random() * 100 + '%';
            img.style.animationDelay = Math.random() * 2 + 's';
            img.style.animationDuration = (Math.random() * 3 + 6) + 's';
            document.getElementById('floatingImages').appendChild(img);
            
            setTimeout(() => {
                img.remove();
            }, 10000);
        }

        function handleYes() {
            // 创建大量爱心和图片
            for (let i = 0; i < 20; i++) {
                setTimeout(() => createHeart(), i * 100);
                setTimeout(() => createFloatingImage(), i * 150);
            }
            
            // 显示成功消息
            setTimeout(() => {
                document.getElementById('mainContainer').style.display = 'none';
                document.getElementById('successMessage').style.display = 'block';
            }, 1000);
        }

        function handleNo() {
            noClickCount++;
            const yesBtn = document.getElementById('yesBtn');
            const noBtn = document.getElementById('noBtn');
            const avatar = document.getElementById('avatar');
            
            // 各种搞怪效果
            switch(noClickCount) {
                case 1:
                    // 放大yes按钮，缩小no按钮
                    yesBtn.style.transform = 'scale(1.2)';
                    noBtn.style.transform = 'scale(0.9)';
                    noBtn.innerHTML = '真的不喜欢？';
                    avatar.src = '008xLqLsly1i0pcras7udj30go0go0u4.jpg';
                    break;
                    
                case 2:
                    // 继续放大yes，继续缩小no
                    yesBtn.style.transform = 'scale(1.4)';
                    noBtn.style.transform = 'scale(0.7)';
                    noBtn.innerHTML = '再想想？';
                    avatar.src = '008xLqLsly1i0pcratvk3j30go0go3zz.jpg';
                    break;
                    
                case 3:
                    // no按钮开始移动
                    yesBtn.style.transform = 'scale(1.6)';
                    noBtn.style.transform = 'scale(0.5)';
                    noBtn.style.position = 'absolute';
                    noBtn.style.left = Math.random() * 300 + 'px';
                    noBtn.style.top = Math.random() * 200 + 'px';
                    noBtn.innerHTML = '别点我！';
                    avatar.src = '008xLqLsly1hz72ypbdjbj30zu0zhtgh.jpg';
                    break;
                    
                case 4:
                    // no按钮变得更小更难点
                    yesBtn.style.transform = 'scale(1.8)';
                    noBtn.style.transform = 'scale(0.3)';
                    noBtn.style.left = Math.random() * 400 + 'px';
                    noBtn.style.top = Math.random() * 300 + 'px';
                    noBtn.innerHTML = '😭';
                    break;
                    
                default:
                    // 最终no按钮消失，只能选yes
                    if (noClickCount >= 5) {
                        noBtn.style.display = 'none';
                        yesBtn.style.transform = 'scale(2)';
                        yesBtn.innerHTML = '只能选我了~ 💖';
                        yesBtn.style.animation = 'pulse 1s infinite';
                    }
                    break;
            }
            
            // 添加一些特效
            createHeart();
            createFloatingImage();
        }

        // 定期创建背景特效
        setInterval(createHeart, 2000);
        setInterval(createFloatingImage, 3000);

        // 添加脉冲动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% { transform: scale(2); }
                50% { transform: scale(2.1); }
                100% { transform: scale(2); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
